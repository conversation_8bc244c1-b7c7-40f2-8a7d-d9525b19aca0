package pers.xdrodger.util;

import java.util.Map;

/**
 * CompressionUtil demonstration class
 * Shows how to use the compression utility
 * 
 * <AUTHOR>
 */
public class CompressionUtilDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Compression Utility Demo ===");
        System.out.println();
        
        // Display supported formats
        System.out.println("Supported compression formats:");
        CompressionUtil.getSupportedFormats().forEach(format -> 
            System.out.println("  " + format));
        System.out.println();
        
        // Display default source directory
        System.out.println("Default source directory: " + CompressionUtil.DEFAULT_SOURCE_DIR);
        System.out.println();
        
        // Test file format checking
        System.out.println("=== File Format Check Test ===");
        String[] testFiles = {
            "document.zip", "archive.rar", "backup.7z", "data.tar",
            "log.gz", "config.bz2", "database.xz", "readme.txt"
        };
        
        for (String fileName : testFiles) {
            boolean isCompressed = CompressionUtil.isCompressedFile(fileName);
            System.out.printf("%-15s -> %s%n", fileName, 
                isCompressed ? "Compressed file" : "Not compressed file");
        }
        System.out.println();
        
        // Test extraction (will show error if directory doesn't exist)
        System.out.println("=== Extraction Test ===");
        try {
            Map<String, String> results = CompressionUtil.extractAllCompressedFiles();
            CompressionUtil.printExtractionResults(results);
        } catch (Exception e) {
            System.out.println("Extraction test completed with message: " + e.getMessage());
        }
        
        System.out.println();
        System.out.println("Demo completed successfully!");
    }
}
