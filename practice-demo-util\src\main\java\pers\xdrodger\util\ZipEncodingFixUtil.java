package pers.xdrodger.util;

import java.io.*;
import java.util.*;
import java.util.zip.*;

/**
 * ZIP file encoding fix utility
 * Specialized tool for fixing ZIP files with encoding issues
 * 
 * <AUTHOR>
 */
public class ZipEncodingFixUtil {
    
    /**
     * Fix ZIP files with encoding issues in the specified directory
     * 
     * @param sourceDir source directory containing problematic ZIP files
     * @param outputDir output directory for fixed files
     */
    public static void fixZipEncodingIssues(String sourceDir, String outputDir) {
        System.out.println("=== ZIP Encoding Fix Tool ===");
        System.out.println("Source Directory: " + sourceDir);
        System.out.println("Output Directory: " + outputDir);
        System.out.println();
        
        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists()) {
            System.err.println("Source directory does not exist: " + sourceDir);
            return;
        }
        
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }
        
        File[] files = sourceDirFile.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".zip"));
        
        if (files == null || files.length == 0) {
            System.out.println("No ZIP files found in the source directory.");
            return;
        }
        
        System.out.println("Found " + files.length + " ZIP files to process...");
        System.out.println();
        
        int successCount = 0;
        int failureCount = 0;
        
        for (File file : files) {
            System.out.print("Processing: " + file.getName() + " ... ");
            
            try {
                if (fixSingleZipFile(file, outputDir)) {
                    successCount++;
                    System.out.println("SUCCESS");
                } else {
                    failureCount++;
                    System.out.println("FAILED");
                }
            } catch (Exception e) {
                failureCount++;
                System.out.println("ERROR: " + e.getMessage());
            }
        }
        
        System.out.println();
        System.out.println("=== Summary ===");
        System.out.println("Total files processed: " + files.length);
        System.out.println("Successfully fixed: " + successCount);
        System.out.println("Failed to fix: " + failureCount);
        System.out.println("Success rate: " + String.format("%.1f%%", (successCount * 100.0 / files.length)));
    }
    
    /**
     * Fix a single ZIP file with encoding issues
     */
    private static boolean fixSingleZipFile(File zipFile, String outputDir) {
        String fixedDirName = "fixed_" + getFileNameWithoutExtension(zipFile.getName());
        File fixedDir = new File(outputDir, fixedDirName);
        fixedDir.mkdirs();
        
        // Try different encodings in order of likelihood
        String[] encodings = {"GBK", "GB2312", "UTF-8", "ISO-8859-1", "Big5"};
        
        for (String encoding : encodings) {
            try {
                int extractedFiles = extractWithEncoding(zipFile, fixedDir, encoding);
                if (extractedFiles > 0) {
                    return true;
                }
            } catch (Exception e) {
                // Try next encoding
            }
        }
        
        return false;
    }
    
    /**
     * Extract ZIP file with specific encoding
     */
    private static int extractWithEncoding(File zipFile, File outputDir, String encoding) throws Exception {
        int extractedCount = 0;
        
        try (ZipInputStream zis = new ZipInputStream(
                new FileInputStream(zipFile), 
                java.nio.charset.Charset.forName(encoding))) {
            
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    try {
                        // Create safe file name
                        String fileName = sanitizeFileName(entry.getName());
                        File outputFile = new File(outputDir, fileName);
                        
                        // Create parent directories
                        File parentDir = outputFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }
                        
                        // Extract file
                        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                            byte[] buffer = new byte[8192];
                            int len;
                            while ((len = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                            }
                            extractedCount++;
                        }
                    } catch (Exception e) {
                        // Skip problematic entries
                    }
                }
                
                try {
                    zis.closeEntry();
                } catch (Exception e) {
                    // Ignore close errors
                }
            }
        }
        
        return extractedCount;
    }
    
    /**
     * Batch fix all ZIP files and create a summary report
     */
    public static void batchFixWithReport(String sourceDir, String outputDir) {
        System.out.println("=== Batch ZIP Encoding Fix with Report ===");
        
        File reportFile = new File(outputDir, "fix_report.txt");
        
        try (PrintWriter writer = new PrintWriter(new FileWriter(reportFile))) {
            writer.println("ZIP Encoding Fix Report");
            writer.println("Generated: " + new Date());
            writer.println("Source Directory: " + sourceDir);
            writer.println("Output Directory: " + outputDir);
            writer.println("=" + repeatString("=", 50));
            writer.println();
            
            File sourceDirFile = new File(sourceDir);
            File[] files = sourceDirFile.listFiles((dir, name) -> 
                name.toLowerCase().endsWith(".zip"));
            
            if (files == null || files.length == 0) {
                writer.println("No ZIP files found.");
                return;
            }
            
            int successCount = 0;
            int failureCount = 0;
            
            for (File file : files) {
                writer.print(file.getName() + " ... ");
                
                try {
                    if (fixSingleZipFile(file, outputDir)) {
                        successCount++;
                        writer.println("SUCCESS");
                    } else {
                        failureCount++;
                        writer.println("FAILED");
                    }
                } catch (Exception e) {
                    failureCount++;
                    writer.println("ERROR: " + e.getMessage());
                }
            }
            
            writer.println();
            writer.println("Summary:");
            writer.println("Total files: " + files.length);
            writer.println("Successfully fixed: " + successCount);
            writer.println("Failed to fix: " + failureCount);
            writer.println("Success rate: " + String.format("%.1f%%", (successCount * 100.0 / files.length)));
            
            System.out.println("Report saved to: " + reportFile.getAbsolutePath());
            
        } catch (IOException e) {
            System.err.println("Failed to write report: " + e.getMessage());
        }
        
        // Also run the regular fix process
        fixZipEncodingIssues(sourceDir, outputDir);
    }
    
    /**
     * Quick fix for the specific "23" directory issue
     */
    public static void quickFix23Directory() {
        String sourceDir = "D:\\腾讯生产待处理文件\\未处理\\23";
        String outputDir = "D:\\腾讯生产待处理文件\\未处理\\23_fixed";
        
        System.out.println("Quick fix for 23 directory encoding issues...");
        fixZipEncodingIssues(sourceDir, outputDir);
    }
    
    /**
     * Sanitize file name to prevent issues
     */
    private static String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unnamed_file";
        }
        
        // Remove or replace problematic characters
        String sanitized = fileName
            .replaceAll("[\\\\/:*?\"<>|]", "_")  // Windows forbidden characters
            .replaceAll("[.]{2,}", ".")          // Multiple dots
            .trim();
        
        // Ensure it's not empty after sanitization
        if (sanitized.isEmpty()) {
            sanitized = "sanitized_file";
        }
        
        return sanitized;
    }
    
    /**
     * Get file name without extension
     */
    private static String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }

    /**
     * Repeat string for Java 8 compatibility (String.repeat() is Java 11+)
     */
    private static String repeatString(String str, int count) {
        if (str == null || count <= 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * Main method for quick testing
     */
    public static void main(String[] args) {
        if (args.length >= 2) {
            // Use command line arguments
            String sourceDir = args[0];
            String outputDir = args[1];
            
            if (args.length >= 3 && "report".equals(args[2])) {
                batchFixWithReport(sourceDir, outputDir);
            } else {
                fixZipEncodingIssues(sourceDir, outputDir);
            }
        } else {
            // Use default directories for the specific issue
            quickFix23Directory();
        }
    }
}
