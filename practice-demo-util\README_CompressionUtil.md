# 压缩文件解压工具 (CompressionUtil)

## 概述

本项目为 `practice-demo-util` 模块添加了压缩文件解压功能，可以自动解压指定目录下的各种压缩文件。

## 功能特性

### 1. SimpleCompressionUtil (基础版本)
- **支持格式**: ZIP, JAR, WAR 文件
- **依赖**: 仅使用 Java 标准库
- **特点**: 轻量级，无外部依赖
- **递归查找**: 支持多层子目录递归查找和解压
- **目录结构**: 保持原有目录结构进行解压

### 2. CompressionUtil (完整版本)
- **支持格式**: ZIP, RAR, 7Z, TAR, GZ, BZ2, XZ 文件
- **依赖**: Hutool, Apache Commons Compress
- **特点**: 功能完整，支持多种压缩格式
- **递归查找**: 支持多层子目录递归查找和解压

## 使用方法

### 基本用法

```java
import pers.xdrodger.util.SimpleCompressionUtil;
import java.util.Map;

// 1. 解压默认目录下的所有压缩文件（非递归）
Map<String, String> results = SimpleCompressionUtil.extractAllCompressedFiles();
SimpleCompressionUtil.printExtractionResults(results);

// 2. 解压指定目录下的所有压缩文件（非递归）
String sourceDir = "D:\\压缩文件目录";
Map<String, String> results2 = SimpleCompressionUtil.extractAllCompressedFiles(sourceDir);

// 3. 解压到指定目标目录（非递归）
String targetDir = "D:\\解压输出目录";
Map<String, String> results3 = SimpleCompressionUtil.extractAllCompressedFiles(sourceDir, targetDir);

// 4. 解压单个文件
boolean success = SimpleCompressionUtil.extractSingleFile("D:\\test.zip", "D:\\output");
```

### 递归查找多层子目录

```java
// 5. 递归解压默认目录及所有子目录下的压缩文件
Map<String, String> recursiveResults = SimpleCompressionUtil.extractAllCompressedFilesRecursive(true);
SimpleCompressionUtil.printExtractionResults(recursiveResults);

// 6. 递归解压指定目录及所有子目录
Map<String, String> results4 = SimpleCompressionUtil.extractAllCompressedFiles(sourceDir, true);

// 7. 递归解压到指定目标目录，保持原有目录结构
Map<String, String> results5 = SimpleCompressionUtil.extractAllCompressedFiles(sourceDir, targetDir, true);
```

### 高级用法

```java
// 检查文件是否为支持的压缩文件
boolean isSupported = SimpleCompressionUtil.isCompressedFile("document.zip");

// 获取支持的压缩格式列表
Set<String> formats = SimpleCompressionUtil.getSupportedFormats();

// 统计压缩文件数量（递归）
Map<String, Integer> counts = SimpleCompressionUtil.countCompressedFiles("D:\\源目录", true);
System.out.println("总计压缩文件: " + counts.get("TOTAL"));
System.out.println("ZIP文件数量: " + counts.get(".ZIP"));

// 显示目录结构（递归）
SimpleCompressionUtil.printDirectoryStructure("D:\\源目录", true);

// 自定义解压逻辑
Map<String, String> results = SimpleCompressionUtil.extractAllCompressedFiles();
for (Map.Entry<String, String> entry : results.entrySet()) {
    String fileName = entry.getKey();
    String status = entry.getValue();

    if (status.startsWith("Extraction successful")) {
        System.out.println("成功解压: " + fileName);
    } else if (status.startsWith("Extraction failed")) {
        System.out.println("解压失败: " + fileName);
    }
}
```

## 递归查找多层子目录功能

### 功能说明
递归查找功能可以深入搜索多层子目录，找到所有嵌套在子文件夹中的压缩文件。这对于处理复杂的文件夹结构特别有用。

### 主要特性
1. **深度搜索**: 自动遍历所有子目录和子子目录
2. **路径保持**: 解压时保持原有的目录结构
3. **统计功能**: 提供文件数量统计和格式分布
4. **可视化**: 显示目录树结构和文件信息
5. **安全限制**: 防止无限递归（最大深度限制）

### 使用示例

```java
// 递归统计压缩文件
Map<String, Integer> counts = SimpleCompressionUtil.countCompressedFiles("D:\\源目录", true);
System.out.println("找到 " + counts.get("TOTAL") + " 个压缩文件");

// 显示目录结构
SimpleCompressionUtil.printDirectoryStructure("D:\\源目录", true);

// 递归解压所有文件
Map<String, String> results = SimpleCompressionUtil.extractAllCompressedFiles("D:\\源目录", "D:\\输出目录", true);
```

### 输出示例
```
=== Directory Structure ===
Source: D:\腾讯生产待处理文件\未处理
Recursive: true

📁 23/
  📦 EXCEL数据_CDG_23年_大陆_00007998_CDG_在线市场部产品部_媒体运营部.zip (524.8 KB)
  📦 EXCEL数据_CDG_23年_大陆_00015564_CDG_在线市场部产品部_数据运营中心部.zip (453.9 KB)
  📁 子目录/
    📦 更多压缩文件.zip (123.4 KB)
```

## 配置说明

### 默认配置
- **默认源目录**: `D:\腾讯生产待处理文件\未处理`
- **默认目标目录**: `{源目录}\extracted`
- **支持的文件格式**: `.zip`, `.jar`, `.war` (SimpleCompressionUtil)
- **递归深度限制**: 最大10层（防止无限递归）

### 自定义配置
可以通过方法参数指定自定义的源目录和目标目录：

```java
// 自定义源目录和目标目录（非递归）
String customSource = "D:\\自定义源目录";
String customTarget = "D:\\自定义目标目录";
Map<String, String> results = SimpleCompressionUtil.extractAllCompressedFiles(customSource, customTarget);

// 自定义源目录和目标目录（递归）
Map<String, String> recursiveResults = SimpleCompressionUtil.extractAllCompressedFiles(customSource, customTarget, true);
```

## 返回值说明

解压方法返回 `Map<String, String>` 类型的结果，其中：
- **Key**: 文件名或状态标识
- **Value**: 解压结果描述

### 常见返回值
- `"Extraction successful -> {路径}"`: 解压成功
- `"Extraction failed"`: 解压失败
- `"Extraction error: {错误信息}"`: 解压异常
- `"ERROR"`: 系统错误（如目录不存在）
- `"INFO"`: 信息提示（如目录为空）
- `"SUMMARY"`: 解压汇总信息

## 示例程序

### 运行演示程序
```bash
# 编译
javac -encoding UTF-8 -d practice-demo-util/target/classes practice-demo-util/src/main/java/pers/xdrodger/util/SimpleCompressionUtil.java

# 运行
java -cp practice-demo-util/target/classes pers.xdrodger.util.SimpleCompressionUtil
```

### 集成到现有项目
```java
public class MyApplication {
    public static void main(String[] args) {
        // 批量解压文件
        Map<String, String> results = SimpleCompressionUtil.extractAllCompressedFiles();
        
        // 处理解压结果
        boolean hasErrors = results.values().stream()
            .anyMatch(status -> status.startsWith("Extraction failed") || status.startsWith("ERROR"));
            
        if (hasErrors) {
            System.err.println("解压过程中发现错误，请检查日志");
        } else {
            System.out.println("所有文件解压完成");
        }
    }
}
```

## 错误处理

### 常见错误及解决方案

1. **源目录不存在**
   - 错误: `"Source directory does not exist or is not valid"`
   - 解决: 检查源目录路径是否正确

2. **无法创建目标目录**
   - 错误: `"Cannot create target directory"`
   - 解决: 检查目标路径权限和磁盘空间

3. **文件格式不支持**
   - 错误: `"Unsupported file format"`
   - 解决: 使用 CompressionUtil 完整版本或转换文件格式

4. **解压异常**
   - 错误: `"Extraction error: {具体错误}"`
   - 解决: 检查文件是否损坏或被占用

## 扩展开发

### 添加新的压缩格式支持
```java
// 1. 在 SUPPORTED_EXTENSIONS 中添加新格式
public static final Set<String> SUPPORTED_EXTENSIONS = new HashSet<>(Arrays.asList(
    ".zip", ".jar", ".war", ".新格式"
));

// 2. 在 extractSingleFile 方法中添加处理逻辑
if (fileName.endsWith(".新格式")) {
    return extract新格式File(sourceFilePath, targetDirPath);
}

// 3. 实现具体的解压方法
private static boolean extract新格式File(String sourceFilePath, String targetDirPath) {
    // 实现解压逻辑
    return true;
}
```

## 性能优化建议

1. **大文件处理**: 对于大型压缩文件，考虑使用多线程解压
2. **内存管理**: 调整缓冲区大小以优化内存使用
3. **批量处理**: 使用线程池并行处理多个文件
4. **进度监控**: 添加解压进度回调接口

## 注意事项

1. **文件路径**: 确保路径中的中文字符编码正确
2. **权限问题**: 运行程序需要对源目录和目标目录的读写权限
3. **磁盘空间**: 确保目标磁盘有足够空间存储解压文件
4. **文件占用**: 确保压缩文件没有被其他程序占用
5. **安全考虑**: 解压前验证文件来源，防止恶意压缩包

## 版本历史

- **v1.0**: 基础版本，支持 ZIP/JAR/WAR 格式
- **v1.1**: 添加完整版本，支持多种压缩格式
- **v1.2**: 优化错误处理和用户体验

## 联系方式

如有问题或建议，请联系开发团队。
