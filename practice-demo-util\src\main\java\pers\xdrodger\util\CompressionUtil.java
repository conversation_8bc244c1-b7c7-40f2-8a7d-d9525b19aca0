package pers.xdrodger.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.sevenz.SevenZArchiveEntry;
import org.apache.commons.compress.archivers.sevenz.SevenZFile;
import org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream;
import org.apache.commons.compress.compressors.xz.XZCompressorInputStream;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.zip.GZIPInputStream;

/**
 * Compression file extraction utility class
 * Used to extract various compressed files in specified directory
 *
 * <AUTHOR>
 */
public class CompressionUtil {

    /**
     * Default source directory path
     */
    public static final String DEFAULT_SOURCE_DIR = "D:\\腾讯生产待处理文件\\未处理";
    
    /**
     * Supported compressed file extensions
     */
    public static final Set<String> SUPPORTED_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz"
    ));

    /**
     * Extract all compressed files in default source directory
     *
     * @return extraction result information
     */
    public static Map<String, String> extractAllCompressedFiles() {
        return extractAllCompressedFiles(DEFAULT_SOURCE_DIR);
    }

    /**
     * Extract all compressed files in specified directory
     *
     * @param sourceDir source directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir) {
        return extractAllCompressedFiles(sourceDir, sourceDir + "\\extracted");
    }

    /**
     * Extract all compressed files in specified directory to target directory
     *
     * @param sourceDir source directory path
     * @param targetDir target directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir, String targetDir) {
        Map<String, String> results = new HashMap<>();
        
        if (StringUtils.isBlank(sourceDir)) {
            results.put("ERROR", "Source directory path cannot be empty");
            return results;
        }

        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            results.put("ERROR", "Source directory does not exist or is not valid: " + sourceDir);
            return results;
        }

        // Create target directory
        File targetDirFile = new File(targetDir);
        if (!targetDirFile.exists()) {
            boolean created = targetDirFile.mkdirs();
            if (!created) {
                results.put("ERROR", "Cannot create target directory: " + targetDir);
                return results;
            }
        }

        // Get all compressed files
        File[] files = sourceDirFile.listFiles();
        if (files == null || files.length == 0) {
            results.put("INFO", "No files found in source directory");
            return results;
        }
        
        int totalFiles = 0;
        int successCount = 0;
        
        for (File file : files) {
            if (file.isFile() && isCompressedFile(file.getName())) {
                totalFiles++;
                String fileName = file.getName();
                try {
                    String extractPath = createExtractionPath(targetDir, fileName);
                    boolean success = extractSingleFile(file.getAbsolutePath(), extractPath);
                    if (success) {
                        successCount++;
                        results.put(fileName, "Extraction successful -> " + extractPath);
                    } else {
                        results.put(fileName, "Extraction failed");
                    }
                } catch (Exception e) {
                    results.put(fileName, "Extraction error: " + e.getMessage());
                }
            }
        }

        results.put("SUMMARY", String.format("Total: %d compressed files, successfully extracted: %d files", totalFiles, successCount));
        return results;
    }

    /**
     * Extract single compressed file
     *
     * @param sourceFilePath source file path
     * @param targetDirPath target directory path
     * @return whether extraction is successful
     */
    public static boolean extractSingleFile(String sourceFilePath, String targetDirPath) {
        if (StringUtils.isBlank(sourceFilePath) || StringUtils.isBlank(targetDirPath)) {
            return false;
        }
        
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return false;
        }
        
        String fileName = sourceFile.getName().toLowerCase();
        
        try {
            // 创建目标目录
            File targetDir = new File(targetDirPath);
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }
            
            if (fileName.endsWith(".zip")) {
                return extractZipFile(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".gz")) {
                return extractGzFile(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".tar")) {
                return extractTarFile(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".7z")) {
                return extract7zFile(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".bz2")) {
                return extractBz2File(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".xz")) {
                return extractXzFile(sourceFilePath, targetDirPath);
            } else if (fileName.endsWith(".rar")) {
                // RAR format requires special handling, not supported currently
                System.out.println("RAR format not supported, recommend using third-party tools: " + sourceFilePath);
                return false;
            } else {
                // For other formats, try using Hutool's ZipUtil
                try {
                    ZipUtil.unzip(sourceFilePath, targetDirPath);
                    return true;
                } catch (Exception e) {
                    System.err.println("Hutool extraction failed: " + e.getMessage());
                    return false;
                }
            }
        } catch (Exception e) {
            System.err.println("File extraction failed: " + sourceFilePath + ", error: " + e.getMessage());
            return false;
        }
    }

    /**
     * Extract ZIP file
     */
    private static boolean extractZipFile(String sourceFilePath, String targetDirPath) {
        try {
            ZipUtil.unzip(sourceFilePath, targetDirPath);
            return true;
        } catch (Exception e) {
            System.err.println("ZIP file extraction failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Extract GZ file
     */
    private static boolean extractGzFile(String sourceFilePath, String targetDirPath) {
        try (FileInputStream fis = new FileInputStream(sourceFilePath);
             GZIPInputStream gzis = new GZIPInputStream(fis)) {

            String fileName = new File(sourceFilePath).getName();
            if (fileName.endsWith(".gz")) {
                fileName = fileName.substring(0, fileName.length() - 3);
            }

            File outputFile = new File(targetDirPath, fileName);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = gzis.read(buffer)) != -1) {
                    fos.write(buffer, 0, len);
                }
            }
            return true;
        } catch (Exception e) {
            System.err.println("GZ file extraction failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Extract TAR file
     */
    private static boolean extractTarFile(String sourceFilePath, String targetDirPath) {
        try (FileInputStream fis = new FileInputStream(sourceFilePath);
             TarArchiveInputStream tais = new TarArchiveInputStream(fis)) {

            TarArchiveEntry entry;
            while ((entry = tais.getNextTarEntry()) != null) {
                if (entry.isDirectory()) {
                    File dir = new File(targetDirPath, entry.getName());
                    dir.mkdirs();
                } else {
                    File outputFile = new File(targetDirPath, entry.getName());
                    outputFile.getParentFile().mkdirs();

                    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = tais.read(buffer)) != -1) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            System.err.println("TAR file extraction failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Extract 7Z file
     */
    private static boolean extract7zFile(String sourceFilePath, String targetDirPath) {
        try (SevenZFile sevenZFile = new SevenZFile(new File(sourceFilePath))) {
            SevenZArchiveEntry entry;
            while ((entry = sevenZFile.getNextEntry()) != null) {
                if (entry.isDirectory()) {
                    File dir = new File(targetDirPath, entry.getName());
                    dir.mkdirs();
                } else {
                    File outputFile = new File(targetDirPath, entry.getName());
                    outputFile.getParentFile().mkdirs();

                    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = sevenZFile.read(buffer)) != -1) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            System.err.println("7Z file extraction failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Extract BZ2 file
     */
    private static boolean extractBz2File(String sourceFilePath, String targetDirPath) {
        try (FileInputStream fis = new FileInputStream(sourceFilePath);
             BZip2CompressorInputStream bzis = new BZip2CompressorInputStream(fis)) {

            String fileName = new File(sourceFilePath).getName();
            if (fileName.endsWith(".bz2")) {
                fileName = fileName.substring(0, fileName.length() - 4);
            }

            File outputFile = new File(targetDirPath, fileName);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = bzis.read(buffer)) != -1) {
                    fos.write(buffer, 0, len);
                }
            }
            return true;
        } catch (Exception e) {
            System.err.println("BZ2 file extraction failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Extract XZ file
     */
    private static boolean extractXzFile(String sourceFilePath, String targetDirPath) {
        try (FileInputStream fis = new FileInputStream(sourceFilePath);
             XZCompressorInputStream xzis = new XZCompressorInputStream(fis)) {

            String fileName = new File(sourceFilePath).getName();
            if (fileName.endsWith(".xz")) {
                fileName = fileName.substring(0, fileName.length() - 3);
            }

            File outputFile = new File(targetDirPath, fileName);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = xzis.read(buffer)) != -1) {
                    fos.write(buffer, 0, len);
                }
            }
            return true;
        } catch (Exception e) {
            System.err.println("XZ file extraction failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if file is a compressed file
     */
    public static boolean isCompressedFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return SUPPORTED_EXTENSIONS.stream().anyMatch(lowerFileName::endsWith);
    }

    /**
     * Create extraction path
     */
    private static String createExtractionPath(String targetDir, String fileName) {
        String nameWithoutExt = FileUtil.getPrefix(fileName);
        return targetDir + File.separator + nameWithoutExt;
    }

    /**
     * Get supported compression formats list
     */
    public static Set<String> getSupportedFormats() {
        return new HashSet<>(SUPPORTED_EXTENSIONS);
    }

    /**
     * Print extraction results
     */
    public static void printExtractionResults(Map<String, String> results) {
        System.out.println("=== Compression File Extraction Results ===");
        for (Map.Entry<String, String> entry : results.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        System.out.println("==========================================");
    }

    /**
     * Main method for testing
     */
    public static void main(String[] args) {
        // Test extraction functionality
        Map<String, String> results = extractAllCompressedFiles();
        printExtractionResults(results);
    }
}
