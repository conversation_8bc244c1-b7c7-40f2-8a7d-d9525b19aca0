package pers.xdrodger.util;

import java.util.Map;
import java.util.Scanner;

/**
 * CompressionUtil使用示例
 * 演示如何使用压缩文件解压工具
 * 
 * <AUTHOR>
 */
public class CompressionUtilExample {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== 压缩文件解压工具使用示例 ===");
        System.out.println();
        
        // 显示支持的压缩格式
        System.out.println("支持的压缩格式:");
        CompressionUtil.getSupportedFormats().forEach(format -> 
            System.out.println("  " + format));
        System.out.println();
        
        // 显示默认源目录
        System.out.println("默认源目录: " + CompressionUtil.DEFAULT_SOURCE_DIR);
        System.out.println();
        
        while (true) {
            System.out.println("请选择操作:");
            System.out.println("1. 解压默认目录下的所有压缩文件");
            System.out.println("2. 解压指定目录下的所有压缩文件");
            System.out.println("3. 解压单个压缩文件");
            System.out.println("4. 检查文件是否为压缩文件");
            System.out.println("0. 退出");
            System.out.print("请输入选项 (0-4): ");
            
            String choice = scanner.nextLine().trim();
            System.out.println();
            
            switch (choice) {
                case "1":
                    extractDefaultDirectory();
                    break;
                case "2":
                    extractCustomDirectory(scanner);
                    break;
                case "3":
                    extractSingleFile(scanner);
                    break;
                case "4":
                    checkCompressedFile(scanner);
                    break;
                case "0":
                    System.out.println("程序退出。");
                    scanner.close();
                    return;
                default:
                    System.out.println("无效选项，请重新选择。");
                    break;
            }
            
            System.out.println();
            System.out.println("按回车键继续...");
            scanner.nextLine();
            System.out.println();
        }
    }
    
    /**
     * 解压默认目录下的所有压缩文件
     */
    private static void extractDefaultDirectory() {
        System.out.println("开始解压默认目录下的所有压缩文件...");
        
        try {
            Map<String, String> results = CompressionUtil.extractAllCompressedFiles();
            CompressionUtil.printExtractionResults(results);
        } catch (Exception e) {
            System.err.println("解压过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 解压指定目录下的所有压缩文件
     */
    private static void extractCustomDirectory(Scanner scanner) {
        System.out.print("请输入源目录路径: ");
        String sourceDir = scanner.nextLine().trim();
        
        if (sourceDir.isEmpty()) {
            System.out.println("源目录路径不能为空。");
            return;
        }
        
        System.out.print("请输入目标目录路径 (留空使用默认): ");
        String targetDir = scanner.nextLine().trim();
        
        System.out.println("开始解压指定目录下的所有压缩文件...");
        
        try {
            Map<String, String> results;
            if (targetDir.isEmpty()) {
                results = CompressionUtil.extractAllCompressedFiles(sourceDir);
            } else {
                results = CompressionUtil.extractAllCompressedFiles(sourceDir, targetDir);
            }
            CompressionUtil.printExtractionResults(results);
        } catch (Exception e) {
            System.err.println("解压过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 解压单个压缩文件
     */
    private static void extractSingleFile(Scanner scanner) {
        System.out.print("请输入压缩文件路径: ");
        String sourceFile = scanner.nextLine().trim();
        
        if (sourceFile.isEmpty()) {
            System.out.println("压缩文件路径不能为空。");
            return;
        }
        
        System.out.print("请输入解压目标目录: ");
        String targetDir = scanner.nextLine().trim();
        
        if (targetDir.isEmpty()) {
            System.out.println("目标目录不能为空。");
            return;
        }
        
        System.out.println("开始解压文件...");
        
        try {
            boolean success = CompressionUtil.extractSingleFile(sourceFile, targetDir);
            if (success) {
                System.out.println("文件解压成功: " + sourceFile + " -> " + targetDir);
            } else {
                System.out.println("文件解压失败: " + sourceFile);
            }
        } catch (Exception e) {
            System.err.println("解压过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 检查文件是否为压缩文件
     */
    private static void checkCompressedFile(Scanner scanner) {
        System.out.print("请输入文件名: ");
        String fileName = scanner.nextLine().trim();
        
        if (fileName.isEmpty()) {
            System.out.println("文件名不能为空。");
            return;
        }
        
        boolean isCompressed = CompressionUtil.isCompressedFile(fileName);
        if (isCompressed) {
            System.out.println("文件 '" + fileName + "' 是支持的压缩文件格式。");
        } else {
            System.out.println("文件 '" + fileName + "' 不是支持的压缩文件格式。");
        }
    }
    
    /**
     * 批量处理示例
     */
    public static void batchProcessExample() {
        System.out.println("=== 批量处理示例 ===");
        
        // 示例1: 解压默认目录
        System.out.println("示例1: 解压默认目录");
        Map<String, String> results1 = CompressionUtil.extractAllCompressedFiles();
        CompressionUtil.printExtractionResults(results1);
        
        // 示例2: 解压指定目录
        System.out.println("\n示例2: 解压指定目录");
        String customSourceDir = "D:\\自定义压缩文件目录";
        String customTargetDir = "D:\\解压输出目录";
        Map<String, String> results2 = CompressionUtil.extractAllCompressedFiles(customSourceDir, customTargetDir);
        CompressionUtil.printExtractionResults(results2);
        
        // 示例3: 解压单个文件
        System.out.println("\n示例3: 解压单个文件");
        String singleFile = "D:\\test.zip";
        String singleTargetDir = "D:\\single_extract";
        boolean success = CompressionUtil.extractSingleFile(singleFile, singleTargetDir);
        System.out.println("单个文件解压结果: " + (success ? "成功" : "失败"));
    }
    
    /**
     * 文件格式检查示例
     */
    public static void fileFormatCheckExample() {
        System.out.println("=== 文件格式检查示例 ===");
        
        String[] testFiles = {
            "document.zip", "archive.rar", "backup.7z", "data.tar",
            "log.gz", "config.bz2", "database.xz", "readme.txt"
        };
        
        for (String fileName : testFiles) {
            boolean isCompressed = CompressionUtil.isCompressedFile(fileName);
            System.out.printf("%-15s -> %s%n", fileName, 
                isCompressed ? "压缩文件" : "非压缩文件");
        }
    }
}
