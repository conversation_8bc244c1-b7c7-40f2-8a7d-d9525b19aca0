package pers.xdrodger.util;

import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.Assert.*;

/**
 * CompressionUtil测试类
 * 
 * <AUTHOR>
 */
public class CompressionUtilTest {
    
    private String testDir;
    private String testSourceDir;
    private String testTargetDir;
    
    @Before
    public void setUp() {
        // 创建测试目录
        testDir = System.getProperty("java.io.tmpdir") + File.separator + "compression_test";
        testSourceDir = testDir + File.separator + "source";
        testTargetDir = testDir + File.separator + "target";
        
        File sourceDirFile = new File(testSourceDir);
        File targetDirFile = new File(testTargetDir);
        
        if (!sourceDirFile.exists()) {
            sourceDirFile.mkdirs();
        }
        if (!targetDirFile.exists()) {
            targetDirFile.mkdirs();
        }
    }
    
    @Test
    public void testIsCompressedFile() {
        assertTrue("ZIP文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.zip"));
        assertTrue("RAR文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.rar"));
        assertTrue("7Z文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.7z"));
        assertTrue("TAR文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.tar"));
        assertTrue("GZ文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.gz"));
        assertTrue("BZ2文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.bz2"));
        assertTrue("XZ文件应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.xz"));
        
        assertFalse("TXT文件不应该被识别为压缩文件", CompressionUtil.isCompressedFile("test.txt"));
        assertFalse("空字符串不应该被识别为压缩文件", CompressionUtil.isCompressedFile(""));
        assertFalse("null不应该被识别为压缩文件", CompressionUtil.isCompressedFile(null));
    }
    
    @Test
    public void testGetSupportedFormats() {
        Set<String> formats = CompressionUtil.getSupportedFormats();
        assertNotNull("支持的格式列表不应该为null", formats);
        assertTrue("应该支持ZIP格式", formats.contains(".zip"));
        assertTrue("应该支持RAR格式", formats.contains(".rar"));
        assertTrue("应该支持7Z格式", formats.contains(".7z"));
        assertTrue("应该支持TAR格式", formats.contains(".tar"));
        assertTrue("应该支持GZ格式", formats.contains(".gz"));
        assertTrue("应该支持BZ2格式", formats.contains(".bz2"));
        assertTrue("应该支持XZ格式", formats.contains(".xz"));
    }
    
    @Test
    public void testExtractAllCompressedFilesWithInvalidDir() {
        Map<String, String> results = CompressionUtil.extractAllCompressedFiles("invalid_directory");
        assertNotNull("结果不应该为null", results);
        assertTrue("应该包含错误信息", results.containsKey("ERROR"));
    }
    
    @Test
    public void testExtractAllCompressedFilesWithEmptyDir() {
        // 清空测试源目录
        File[] files = new File(testSourceDir).listFiles();
        if (files != null) {
            for (File file : files) {
                file.delete();
            }
        }
        
        Map<String, String> results = CompressionUtil.extractAllCompressedFiles(testSourceDir, testTargetDir);
        assertNotNull("结果不应该为null", results);
        assertTrue("应该包含信息提示", results.containsKey("INFO"));
    }
    
    @Test
    public void testCreateAndExtractZipFile() throws IOException {
        // 创建一个测试ZIP文件
        String zipFilePath = testSourceDir + File.separator + "test.zip";
        createTestZipFile(zipFilePath);
        
        // 测试解压
        String extractPath = testTargetDir + File.separator + "test_zip_extract";
        boolean success = CompressionUtil.extractSingleFile(zipFilePath, extractPath);
        
        assertTrue("ZIP文件解压应该成功", success);
        
        // 验证解压结果
        File extractedDir = new File(extractPath);
        assertTrue("解压目录应该存在", extractedDir.exists());
        
        File extractedFile = new File(extractPath, "test.txt");
        assertTrue("解压的文件应该存在", extractedFile.exists());
    }
    
    @Test
    public void testExtractSingleFileWithInvalidPath() {
        boolean result = CompressionUtil.extractSingleFile("", "");
        assertFalse("空路径应该返回false", result);
        
        result = CompressionUtil.extractSingleFile("invalid_file.zip", testTargetDir);
        assertFalse("不存在的文件应该返回false", result);
    }
    
    @Test
    public void testPrintExtractionResults() {
        // 这个测试主要验证方法不会抛出异常
        Map<String, String> results = CompressionUtil.extractAllCompressedFiles(testSourceDir, testTargetDir);
        
        // 应该不会抛出异常
        assertDoesNotThrow(() -> CompressionUtil.printExtractionResults(results));
    }
    
    /**
     * 创建测试用的ZIP文件
     */
    private void createTestZipFile(String zipFilePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            
            // 添加一个测试文件到ZIP中
            ZipEntry entry = new ZipEntry("test.txt");
            zos.putNextEntry(entry);
            zos.write("This is a test file content.".getBytes());
            zos.closeEntry();
        }
    }
    
    /**
     * 断言方法不抛出异常的辅助方法（JUnit 4兼容）
     */
    private void assertDoesNotThrow(Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            fail("方法不应该抛出异常: " + e.getMessage());
        }
    }
}
