package pers.xdrodger.util;

import java.io.*;
import java.util.*;
import java.util.zip.*;

/**
 * Simple compression file extraction utility class
 * Uses only Java standard library for basic ZIP file extraction
 * 
 * <AUTHOR>
 */
public class SimpleCompressionUtil {
    
    /**
     * Default source directory path
     */
    public static final String DEFAULT_SOURCE_DIR = "D:\\腾讯生产待处理文件\\未处理";
    
    /**
     * Supported compressed file extensions (basic support)
     */
    public static final Set<String> SUPPORTED_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".zip", ".jar", ".war"
    ));
    
    /**
     * Extract all compressed files in default source directory
     * 
     * @return extraction result information
     */
    public static Map<String, String> extractAllCompressedFiles() {
        return extractAllCompressedFiles(DEFAULT_SOURCE_DIR);
    }
    
    /**
     * Extract all compressed files in specified directory
     *
     * @param sourceDir source directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir) {
        return extractAllCompressedFiles(sourceDir, sourceDir + "\\extracted");
    }

    /**
     * Extract all compressed files in specified directory with recursive search
     *
     * @param sourceDir source directory path
     * @param recursive whether to search subdirectories recursively
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir, boolean recursive) {
        return extractAllCompressedFiles(sourceDir, sourceDir + "\\extracted", recursive);
    }

    /**
     * Extract all compressed files in default directory with recursive search
     *
     * @param recursive whether to search subdirectories recursively
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFilesRecursive(boolean recursive) {
        return extractAllCompressedFiles(DEFAULT_SOURCE_DIR, recursive);
    }
    
    /**
     * Extract all compressed files in specified directory to target directory
     *
     * @param sourceDir source directory path
     * @param targetDir target directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir, String targetDir) {
        return extractAllCompressedFiles(sourceDir, targetDir, false);
    }

    /**
     * Extract all compressed files in specified directory to target directory with recursive option
     *
     * @param sourceDir source directory path
     * @param targetDir target directory path
     * @param recursive whether to search subdirectories recursively
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir, String targetDir, boolean recursive) {
        Map<String, String> results = new HashMap<>();

        if (isBlank(sourceDir)) {
            results.put("ERROR", "Source directory path cannot be empty");
            return results;
        }

        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            results.put("ERROR", "Source directory does not exist or is not valid: " + sourceDir);
            return results;
        }

        // Create target directory
        File targetDirFile = new File(targetDir);
        if (!targetDirFile.exists()) {
            boolean created = targetDirFile.mkdirs();
            if (!created) {
                results.put("ERROR", "Cannot create target directory: " + targetDir);
                return results;
            }
        }

        // Get all compressed files (with or without recursive search)
        List<File> compressedFiles = findCompressedFiles(sourceDirFile, recursive);

        if (compressedFiles.isEmpty()) {
            results.put("INFO", "No compressed files found in source directory" + (recursive ? " and subdirectories" : ""));
            return results;
        }

        int totalFiles = compressedFiles.size();
        int successCount = 0;

        for (File file : compressedFiles) {
            String fileName = file.getName();
            String relativePath = getRelativePath(sourceDirFile, file);
            try {
                String extractPath = createExtractionPath(targetDir, fileName, relativePath);
                boolean success = extractSingleFile(file.getAbsolutePath(), extractPath);
                if (success) {
                    successCount++;
                    results.put(relativePath, "Extraction successful -> " + extractPath);
                } else {
                    results.put(relativePath, "Extraction failed");
                }
            } catch (Exception e) {
                results.put(relativePath, "Extraction error: " + e.getMessage());
            }
        }

        results.put("SUMMARY", String.format("Total: %d compressed files, successfully extracted: %d files", totalFiles, successCount));
        return results;
    }
    
    /**
     * Extract single compressed file
     * 
     * @param sourceFilePath source file path
     * @param targetDirPath target directory path
     * @return whether extraction is successful
     */
    public static boolean extractSingleFile(String sourceFilePath, String targetDirPath) {
        if (isBlank(sourceFilePath) || isBlank(targetDirPath)) {
            return false;
        }
        
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return false;
        }
        
        String fileName = sourceFile.getName().toLowerCase();
        
        try {
            // Create target directory
            File targetDir = new File(targetDirPath);
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }
            
            if (fileName.endsWith(".zip") || fileName.endsWith(".jar") || fileName.endsWith(".war")) {
                return extractZipFile(sourceFilePath, targetDirPath);
            } else {
                System.out.println("Unsupported file format: " + sourceFilePath);
                return false;
            }
        } catch (Exception e) {
            System.err.println("File extraction failed: " + sourceFilePath + ", error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Extract ZIP file using Java standard library with enhanced error handling
     */
    private static boolean extractZipFile(String sourceFilePath, String targetDirPath) {
        File sourceFile = new File(sourceFilePath);

        // First, validate the ZIP file
        if (!isValidZipFile(sourceFile)) {
            System.err.println("Invalid or corrupted ZIP file: " + sourceFilePath);
            return false;
        }

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(sourceFilePath))) {
            ZipEntry entry;
            int extractedFiles = 0;

            while ((entry = zis.getNextEntry()) != null) {
                try {
                    // Security check: prevent path traversal attacks
                    if (entry.getName().contains("..")) {
                        System.err.println("Skipping potentially dangerous entry: " + entry.getName());
                        zis.closeEntry();
                        continue;
                    }

                    File outputFile = new File(targetDirPath, entry.getName());

                    if (entry.isDirectory()) {
                        if (!outputFile.exists()) {
                            outputFile.mkdirs();
                        }
                    } else {
                        // Create parent directories if they don't exist
                        File parentDir = outputFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }

                        // Extract file with size limit check
                        if (entry.getSize() > 100 * 1024 * 1024) { // 100MB limit
                            System.err.println("File too large, skipping: " + entry.getName() + " (" + formatFileSize(entry.getSize()) + ")");
                            zis.closeEntry();
                            continue;
                        }

                        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                            byte[] buffer = new byte[8192]; // Larger buffer for better performance
                            int len;
                            long totalBytes = 0;

                            while ((len = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, len);
                                totalBytes += len;

                                // Additional size check during extraction
                                if (totalBytes > 100 * 1024 * 1024) {
                                    System.err.println("File size exceeded limit during extraction: " + entry.getName());
                                    break;
                                }
                            }
                            extractedFiles++;
                        }
                    }
                } catch (Exception entryException) {
                    System.err.println("Error extracting entry '" + entry.getName() + "': " + entryException.getMessage());
                    // Continue with next entry instead of failing completely
                } finally {
                    try {
                        zis.closeEntry();
                    } catch (Exception closeException) {
                        // Ignore close exceptions
                    }
                }
            }

            System.out.println("Successfully extracted " + extractedFiles + " files from: " + sourceFile.getName());
            return extractedFiles > 0;

        } catch (Exception e) {
            System.err.println("ZIP file extraction failed for '" + sourceFilePath + "': " + e.getMessage());

            // Try alternative extraction method for corrupted files
            return tryAlternativeExtraction(sourceFilePath, targetDirPath);
        }
    }

    /**
     * Validate if a file is a valid ZIP file
     */
    private static boolean isValidZipFile(File file) {
        if (!file.exists() || !file.isFile()) {
            return false;
        }

        // Check file size (empty files are invalid)
        if (file.length() < 22) { // Minimum ZIP file size
            System.err.println("File too small to be a valid ZIP: " + file.getName());
            return false;
        }

        // Check ZIP file signature
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] signature = new byte[4];
            if (fis.read(signature) == 4) {
                // ZIP file signatures: PK\003\004 (local file header) or PK\005\006 (central directory)
                if ((signature[0] == 0x50 && signature[1] == 0x4B) &&
                    (signature[2] == 0x03 && signature[3] == 0x04 ||
                     signature[2] == 0x05 && signature[3] == 0x06)) {
                    return true;
                }
            }
            System.err.println("Invalid ZIP file signature: " + file.getName());
            return false;
        } catch (Exception e) {
            System.err.println("Error validating ZIP file: " + file.getName() + " - " + e.getMessage());
            return false;
        }
    }

    /**
     * Try alternative extraction method for potentially corrupted ZIP files
     */
    private static boolean tryAlternativeExtraction(String sourceFilePath, String targetDirPath) {
        System.out.println("Attempting alternative extraction method for: " + sourceFilePath);

        try {
            // Try with different encoding
            try (ZipInputStream zis = new ZipInputStream(new FileInputStream(sourceFilePath), java.nio.charset.Charset.forName("GBK"))) {
                ZipEntry entry;
                int extractedFiles = 0;

                while ((entry = zis.getNextEntry()) != null) {
                    try {
                        if (!entry.isDirectory()) {
                            File outputFile = new File(targetDirPath, "recovered_" + entry.getName());
                            outputFile.getParentFile().mkdirs();

                            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = zis.read(buffer)) > 0) {
                                    fos.write(buffer, 0, len);
                                }
                                extractedFiles++;
                            }
                        }
                        zis.closeEntry();
                    } catch (Exception entryException) {
                        // Skip problematic entries
                        try { zis.closeEntry(); } catch (Exception ignored) {}
                    }
                }

                if (extractedFiles > 0) {
                    System.out.println("Alternative extraction recovered " + extractedFiles + " files");
                    return true;
                }
            }
        } catch (Exception e) {
            System.err.println("Alternative extraction also failed: " + e.getMessage());
        }

        return false;
    }
    
    /**
     * Check if file is a compressed file
     */
    public static boolean isCompressedFile(String fileName) {
        if (isBlank(fileName)) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return SUPPORTED_EXTENSIONS.stream().anyMatch(lowerFileName::endsWith);
    }
    
    /**
     * Find all compressed files in directory (with optional recursive search)
     *
     * @param directory the directory to search
     * @param recursive whether to search subdirectories recursively
     * @return list of compressed files found
     */
    private static List<File> findCompressedFiles(File directory, boolean recursive) {
        List<File> compressedFiles = new ArrayList<>();

        File[] files = directory.listFiles();
        if (files == null) {
            return compressedFiles;
        }

        for (File file : files) {
            if (file.isFile() && isCompressedFile(file.getName())) {
                compressedFiles.add(file);
            } else if (file.isDirectory() && recursive) {
                // Recursively search subdirectories
                compressedFiles.addAll(findCompressedFiles(file, recursive));
            }
        }

        return compressedFiles;
    }

    /**
     * Get relative path from base directory to target file
     *
     * @param baseDir base directory
     * @param targetFile target file
     * @return relative path string
     */
    private static String getRelativePath(File baseDir, File targetFile) {
        String basePath = baseDir.getAbsolutePath();
        String targetPath = targetFile.getAbsolutePath();

        if (targetPath.startsWith(basePath)) {
            String relativePath = targetPath.substring(basePath.length());
            if (relativePath.startsWith(File.separator)) {
                relativePath = relativePath.substring(1);
            }
            return relativePath;
        }

        return targetFile.getName();
    }

    /**
     * Create extraction path with relative path consideration
     */
    private static String createExtractionPath(String targetDir, String fileName) {
        String nameWithoutExt = getFileNameWithoutExtension(fileName);
        return targetDir + File.separator + nameWithoutExt;
    }

    /**
     * Create extraction path with relative path consideration
     *
     * @param targetDir target directory
     * @param fileName file name
     * @param relativePath relative path from source directory
     * @return extraction path
     */
    private static String createExtractionPath(String targetDir, String fileName, String relativePath) {
        String nameWithoutExt = getFileNameWithoutExtension(fileName);

        // Create subdirectory structure based on relative path
        String relativeDir = relativePath.contains(File.separator)
            ? relativePath.substring(0, relativePath.lastIndexOf(File.separator))
            : "";

        if (!isBlank(relativeDir)) {
            return targetDir + File.separator + relativeDir + File.separator + nameWithoutExt;
        } else {
            return targetDir + File.separator + nameWithoutExt;
        }
    }
    
    /**
     * Get supported compression formats list
     */
    public static Set<String> getSupportedFormats() {
        return new HashSet<>(SUPPORTED_EXTENSIONS);
    }

    /**
     * Validate and diagnose ZIP files in a directory
     *
     * @param sourceDir source directory path
     * @param recursive whether to search subdirectories recursively
     * @return map containing validation results
     */
    public static Map<String, String> validateZipFiles(String sourceDir, boolean recursive) {
        Map<String, String> results = new HashMap<>();

        if (isBlank(sourceDir)) {
            results.put("ERROR", "Source directory path cannot be empty");
            return results;
        }

        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            results.put("ERROR", "Source directory does not exist: " + sourceDir);
            return results;
        }

        List<File> compressedFiles = findCompressedFiles(sourceDirFile, recursive);

        if (compressedFiles.isEmpty()) {
            results.put("INFO", "No compressed files found");
            return results;
        }

        int validFiles = 0;
        int invalidFiles = 0;
        int corruptedFiles = 0;

        for (File file : compressedFiles) {
            String relativePath = getRelativePath(sourceDirFile, file);

            try {
                if (isValidZipFile(file)) {
                    // Try to read the ZIP file structure
                    if (canReadZipStructure(file)) {
                        validFiles++;
                        results.put(relativePath, "VALID - " + formatFileSize(file.length()));
                    } else {
                        corruptedFiles++;
                        results.put(relativePath, "CORRUPTED - Structure damaged");
                    }
                } else {
                    invalidFiles++;
                    results.put(relativePath, "INVALID - Not a valid ZIP file");
                }
            } catch (Exception e) {
                invalidFiles++;
                results.put(relativePath, "ERROR - " + e.getMessage());
            }
        }

        results.put("SUMMARY", String.format("Total: %d files, Valid: %d, Corrupted: %d, Invalid: %d",
                compressedFiles.size(), validFiles, corruptedFiles, invalidFiles));

        return results;
    }

    /**
     * Check if ZIP file structure can be read
     */
    private static boolean canReadZipStructure(File file) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(file))) {
            ZipEntry entry;
            int entryCount = 0;

            while ((entry = zis.getNextEntry()) != null && entryCount < 10) {
                // Just try to read the entry structure, don't extract
                entry.getName(); // This will throw exception if entry is corrupted
                zis.closeEntry();
                entryCount++;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Attempt to repair corrupted ZIP files by extracting recoverable content
     *
     * @param sourceDir source directory path
     * @param targetDir target directory for repaired files
     * @param recursive whether to search subdirectories recursively
     * @return repair results
     */
    public static Map<String, String> repairCorruptedZipFiles(String sourceDir, String targetDir, boolean recursive) {
        Map<String, String> results = new HashMap<>();

        // First validate all ZIP files
        Map<String, String> validationResults = validateZipFiles(sourceDir, recursive);

        File sourceDirFile = new File(sourceDir);
        File targetDirFile = new File(targetDir);

        if (!targetDirFile.exists()) {
            targetDirFile.mkdirs();
        }

        int repairedFiles = 0;
        int unreparableFiles = 0;

        for (Map.Entry<String, String> entry : validationResults.entrySet()) {
            String relativePath = entry.getKey();
            String status = entry.getValue();

            if (status.startsWith("CORRUPTED") || status.startsWith("ERROR")) {
                File sourceFile = new File(sourceDirFile, relativePath);
                if (sourceFile.exists() && sourceFile.isFile()) {

                    String repairTargetDir = targetDir + File.separator + "repaired_" +
                            getFileNameWithoutExtension(sourceFile.getName());

                    if (tryAlternativeExtraction(sourceFile.getAbsolutePath(), repairTargetDir)) {
                        repairedFiles++;
                        results.put(relativePath, "REPAIRED - Partial content recovered");
                    } else {
                        unreparableFiles++;
                        results.put(relativePath, "UNREPARABLE - Cannot recover any content");
                    }
                }
            }
        }

        results.put("REPAIR_SUMMARY", String.format("Repaired: %d files, Unreparable: %d files",
                repairedFiles, unreparableFiles));

        return results;
    }

    /**
     * Count compressed files in directory with recursive option
     *
     * @param sourceDir source directory path
     * @param recursive whether to search subdirectories recursively
     * @return map containing file count information
     */
    public static Map<String, Integer> countCompressedFiles(String sourceDir, boolean recursive) {
        Map<String, Integer> counts = new HashMap<>();

        if (isBlank(sourceDir)) {
            counts.put("ERROR", -1);
            return counts;
        }

        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            counts.put("ERROR", -1);
            return counts;
        }

        List<File> compressedFiles = findCompressedFiles(sourceDirFile, recursive);
        counts.put("TOTAL", compressedFiles.size());

        // Count by file extension
        for (File file : compressedFiles) {
            String fileName = file.getName().toLowerCase();
            for (String ext : SUPPORTED_EXTENSIONS) {
                if (fileName.endsWith(ext)) {
                    counts.put(ext.toUpperCase(), counts.getOrDefault(ext.toUpperCase(), 0) + 1);
                    break;
                }
            }
        }

        return counts;
    }

    /**
     * Print directory structure with compressed files
     *
     * @param sourceDir source directory path
     * @param recursive whether to search subdirectories recursively
     */
    public static void printDirectoryStructure(String sourceDir, boolean recursive) {
        if (isBlank(sourceDir)) {
            System.out.println("Source directory path cannot be empty");
            return;
        }

        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            System.out.println("Source directory does not exist: " + sourceDir);
            return;
        }

        System.out.println("=== Directory Structure ===");
        System.out.println("Source: " + sourceDir);
        System.out.println("Recursive: " + recursive);
        System.out.println();

        printDirectoryStructureRecursive(sourceDirFile, "", recursive, 0);
    }

    /**
     * Recursively print directory structure
     */
    private static void printDirectoryStructureRecursive(File dir, String prefix, boolean recursive, int depth) {
        if (depth > 10) { // Prevent infinite recursion
            System.out.println(prefix + "... (max depth reached)");
            return;
        }

        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isFile() && isCompressedFile(file.getName())) {
                System.out.println(prefix + "📦 " + file.getName() + " (" + formatFileSize(file.length()) + ")");
            } else if (file.isDirectory() && recursive) {
                System.out.println(prefix + "📁 " + file.getName() + "/");
                printDirectoryStructureRecursive(file, prefix + "  ", recursive, depth + 1);
            }
        }
    }

    /**
     * Format file size for display
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        }
        if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024));
        }
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * Print extraction results
     */
    public static void printExtractionResults(Map<String, String> results) {
        System.out.println("=== Compression File Extraction Results ===");
        for (Map.Entry<String, String> entry : results.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        System.out.println("==========================================");
    }
    
    /**
     * Helper method to check if string is blank
     */
    private static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * Helper method to get filename without extension
     */
    private static String getFileNameWithoutExtension(String fileName) {
        if (isBlank(fileName)) {
            return fileName;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
    
    /**
     * Main method for testing
     */
    public static void main(String[] args) {
        System.out.println("=== Simple Compression Utility Demo ===");
        System.out.println();
        
        // Display supported formats
        System.out.println("Supported compression formats:");
        getSupportedFormats().forEach(format -> 
            System.out.println("  " + format));
        System.out.println();
        
        // Display default source directory
        System.out.println("Default source directory: " + DEFAULT_SOURCE_DIR);
        System.out.println();
        
        // Test file format checking
        System.out.println("=== File Format Check Test ===");
        String[] testFiles = {
            "document.zip", "application.jar", "webapp.war", "archive.rar", "readme.txt"
        };
        
        for (String fileName : testFiles) {
            boolean isCompressed = isCompressedFile(fileName);
            System.out.printf("%-15s -> %s%n", fileName, 
                isCompressed ? "Supported compressed file" : "Not supported");
        }
        System.out.println();

        // Test ZIP file validation
        System.out.println("=== ZIP File Validation Test ===");
        try {
            String testDir = DEFAULT_SOURCE_DIR + "\\23"; // Focus on the problematic directory
            Map<String, String> validationResults = validateZipFiles(testDir, false);
            System.out.println("Validation results for directory: " + testDir);

            int displayCount = 0;
            for (Map.Entry<String, String> entry : validationResults.entrySet()) {
                if (displayCount < 10 || entry.getKey().equals("SUMMARY")) { // Show first 10 + summary
                    System.out.println("  " + entry.getKey() + ": " + entry.getValue());
                    displayCount++;
                }
            }
            if (validationResults.size() > 11) {
                System.out.println("  ... and " + (validationResults.size() - 11) + " more files");
            }
        } catch (Exception e) {
            System.out.println("ZIP validation test completed with message: " + e.getMessage());
        }

        System.out.println();

        // Test file counting
        System.out.println("=== File Count Test ===");
        try {
            Map<String, Integer> counts = countCompressedFiles(DEFAULT_SOURCE_DIR, false);
            System.out.println("Non-recursive count:");
            for (Map.Entry<String, Integer> entry : counts.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }

            System.out.println();
            Map<String, Integer> recursiveCounts = countCompressedFiles(DEFAULT_SOURCE_DIR, true);
            System.out.println("Recursive count:");
            for (Map.Entry<String, Integer> entry : recursiveCounts.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } catch (Exception e) {
            System.out.println("File count test completed with message: " + e.getMessage());
        }

        System.out.println();

        // Test directory structure display
        System.out.println("=== Directory Structure Test ===");
        try {
            printDirectoryStructure(DEFAULT_SOURCE_DIR, true);
        } catch (Exception e) {
            System.out.println("Directory structure test completed with message: " + e.getMessage());
        }

        System.out.println();

        // Test extraction (will show error if directory doesn't exist)
        System.out.println("=== Extraction Test (Non-Recursive) ===");
        try {
            Map<String, String> results = extractAllCompressedFiles();
            printExtractionResults(results);
        } catch (Exception e) {
            System.out.println("Extraction test completed with message: " + e.getMessage());
        }

        System.out.println();
        System.out.println("=== Extraction Test (Recursive) ===");
        try {
            Map<String, String> results = extractAllCompressedFilesRecursive(true);
            printExtractionResults(results);
        } catch (Exception e) {
            System.out.println("Recursive extraction test completed with message: " + e.getMessage());
        }

        System.out.println();
        System.out.println("Demo completed successfully!");
    }
}
