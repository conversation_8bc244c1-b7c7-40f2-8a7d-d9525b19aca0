package pers.xdrodger.util;

import java.io.*;
import java.util.*;
import java.util.zip.*;

/**
 * Simple compression file extraction utility class
 * Uses only Java standard library for basic ZIP file extraction
 * 
 * <AUTHOR>
 */
public class SimpleCompressionUtil {
    
    /**
     * Default source directory path
     */
    public static final String DEFAULT_SOURCE_DIR = "D:\\腾讯生产待处理文件\\未处理";
    
    /**
     * Supported compressed file extensions (basic support)
     */
    public static final Set<String> SUPPORTED_EXTENSIONS = new HashSet<>(Arrays.asList(
            ".zip", ".jar", ".war"
    ));
    
    /**
     * Extract all compressed files in default source directory
     * 
     * @return extraction result information
     */
    public static Map<String, String> extractAllCompressedFiles() {
        return extractAllCompressedFiles(DEFAULT_SOURCE_DIR);
    }
    
    /**
     * Extract all compressed files in specified directory
     * 
     * @param sourceDir source directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir) {
        return extractAllCompressedFiles(sourceDir, sourceDir + "\\extracted");
    }
    
    /**
     * Extract all compressed files in specified directory to target directory
     * 
     * @param sourceDir source directory path
     * @param targetDir target directory path
     * @return extraction result information, key is filename, value is result status
     */
    public static Map<String, String> extractAllCompressedFiles(String sourceDir, String targetDir) {
        Map<String, String> results = new HashMap<>();
        
        if (isBlank(sourceDir)) {
            results.put("ERROR", "Source directory path cannot be empty");
            return results;
        }
        
        File sourceDirFile = new File(sourceDir);
        if (!sourceDirFile.exists() || !sourceDirFile.isDirectory()) {
            results.put("ERROR", "Source directory does not exist or is not valid: " + sourceDir);
            return results;
        }
        
        // Create target directory
        File targetDirFile = new File(targetDir);
        if (!targetDirFile.exists()) {
            boolean created = targetDirFile.mkdirs();
            if (!created) {
                results.put("ERROR", "Cannot create target directory: " + targetDir);
                return results;
            }
        }
        
        // Get all compressed files
        File[] files = sourceDirFile.listFiles();
        if (files == null || files.length == 0) {
            results.put("INFO", "No files found in source directory");
            return results;
        }
        
        int totalFiles = 0;
        int successCount = 0;
        
        for (File file : files) {
            if (file.isFile() && isCompressedFile(file.getName())) {
                totalFiles++;
                String fileName = file.getName();
                try {
                    String extractPath = createExtractionPath(targetDir, fileName);
                    boolean success = extractSingleFile(file.getAbsolutePath(), extractPath);
                    if (success) {
                        successCount++;
                        results.put(fileName, "Extraction successful -> " + extractPath);
                    } else {
                        results.put(fileName, "Extraction failed");
                    }
                } catch (Exception e) {
                    results.put(fileName, "Extraction error: " + e.getMessage());
                }
            }
        }
        
        results.put("SUMMARY", String.format("Total: %d compressed files, successfully extracted: %d files", totalFiles, successCount));
        return results;
    }
    
    /**
     * Extract single compressed file
     * 
     * @param sourceFilePath source file path
     * @param targetDirPath target directory path
     * @return whether extraction is successful
     */
    public static boolean extractSingleFile(String sourceFilePath, String targetDirPath) {
        if (isBlank(sourceFilePath) || isBlank(targetDirPath)) {
            return false;
        }
        
        File sourceFile = new File(sourceFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return false;
        }
        
        String fileName = sourceFile.getName().toLowerCase();
        
        try {
            // Create target directory
            File targetDir = new File(targetDirPath);
            if (!targetDir.exists()) {
                targetDir.mkdirs();
            }
            
            if (fileName.endsWith(".zip") || fileName.endsWith(".jar") || fileName.endsWith(".war")) {
                return extractZipFile(sourceFilePath, targetDirPath);
            } else {
                System.out.println("Unsupported file format: " + sourceFilePath);
                return false;
            }
        } catch (Exception e) {
            System.err.println("File extraction failed: " + sourceFilePath + ", error: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Extract ZIP file using Java standard library
     */
    private static boolean extractZipFile(String sourceFilePath, String targetDirPath) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(sourceFilePath))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File outputFile = new File(targetDirPath, entry.getName());
                
                if (entry.isDirectory()) {
                    outputFile.mkdirs();
                } else {
                    // Create parent directories if they don't exist
                    outputFile.getParentFile().mkdirs();
                    
                    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                zis.closeEntry();
            }
            return true;
        } catch (Exception e) {
            System.err.println("ZIP file extraction failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if file is a compressed file
     */
    public static boolean isCompressedFile(String fileName) {
        if (isBlank(fileName)) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return SUPPORTED_EXTENSIONS.stream().anyMatch(lowerFileName::endsWith);
    }
    
    /**
     * Create extraction path
     */
    private static String createExtractionPath(String targetDir, String fileName) {
        String nameWithoutExt = getFileNameWithoutExtension(fileName);
        return targetDir + File.separator + nameWithoutExt;
    }
    
    /**
     * Get supported compression formats list
     */
    public static Set<String> getSupportedFormats() {
        return new HashSet<>(SUPPORTED_EXTENSIONS);
    }
    
    /**
     * Print extraction results
     */
    public static void printExtractionResults(Map<String, String> results) {
        System.out.println("=== Compression File Extraction Results ===");
        for (Map.Entry<String, String> entry : results.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        System.out.println("==========================================");
    }
    
    /**
     * Helper method to check if string is blank
     */
    private static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * Helper method to get filename without extension
     */
    private static String getFileNameWithoutExtension(String fileName) {
        if (isBlank(fileName)) {
            return fileName;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
    
    /**
     * Main method for testing
     */
    public static void main(String[] args) {
        System.out.println("=== Simple Compression Utility Demo ===");
        System.out.println();
        
        // Display supported formats
        System.out.println("Supported compression formats:");
        getSupportedFormats().forEach(format -> 
            System.out.println("  " + format));
        System.out.println();
        
        // Display default source directory
        System.out.println("Default source directory: " + DEFAULT_SOURCE_DIR);
        System.out.println();
        
        // Test file format checking
        System.out.println("=== File Format Check Test ===");
        String[] testFiles = {
            "document.zip", "application.jar", "webapp.war", "archive.rar", "readme.txt"
        };
        
        for (String fileName : testFiles) {
            boolean isCompressed = isCompressedFile(fileName);
            System.out.printf("%-15s -> %s%n", fileName, 
                isCompressed ? "Supported compressed file" : "Not supported");
        }
        System.out.println();
        
        // Test extraction (will show error if directory doesn't exist)
        System.out.println("=== Extraction Test ===");
        try {
            Map<String, String> results = extractAllCompressedFiles();
            printExtractionResults(results);
        } catch (Exception e) {
            System.out.println("Extraction test completed with message: " + e.getMessage());
        }
        
        System.out.println();
        System.out.println("Demo completed successfully!");
    }
}
